const { groq } = require("@ai-sdk/groq");
const { google } = require("@ai-sdk/google");
const {
  getNodeAutoInstrumentations,
} = require("@opentelemetry/auto-instrumentations-node");
const { NodeSDK } = require("@opentelemetry/sdk-node");
const {
  generateText: aiGenerateText,
  experimental_createMCPClient: createMCPClient,
} = require("ai");
const { Experimental_StdioMCPTransport } = require("ai/mcp-stdio");
const { LangfuseExporter } = require("langfuse-vercel");
const { configService } = require("./configService");
const logger = require("../utils/logger");

// Initialize SDK once at module load time
const sdk = new NodeSDK({
  traceExporter: new LangfuseExporter(),
  instrumentations: [getNodeAutoInstrumentations()],
});

const transport = new Experimental_StdioMCPTransport({
  command: "npx",
  args: [
    "-y",
    "@smithery/cli@latest",
    "run",
    "@big-omega/mem0-mcp",
    "--key",
    "e350793c-8337-47e3-8da1-f45ed9d164ce",
    "--profile",
    "ripe-felidae-lOKJL8",
  ],
  // headers: {
  //   Authorization: "m0-eU2ROBfm0sAPMA6Eqk0GFAjC7VfO8xpR4zurzCSc",
  // },
});

const memoryMcpClient = await createMCPClient({
  transport,
});

// Start the SDK once
sdk.start();

// Handle graceful shutdown
process.on("SIGTERM", async () => {
  await sdk.shutdown();
});

process.on("SIGINT", async () => {
  await sdk.shutdown();
});

async function generateText(messages, systemPrompt, conversationId = null) {
  // Get current configuration
  const provider = configService.get("provider");
  const model = configService.get("model");
  const temperature = configService.get("temperature");
  let memoryMcpClient;
  let searchMcpClient;

  try {
    // memoryMcpClient = await createMCPClient({
    //   transport: {
    //     type: "sse",
    //     url: "https://mcp.supermemory.ai/6Wy3fW0xlIJJTSIKN7C9H/sse",
    //   },
    // });

    // const transport2 = new Experimental_StdioMCPTransport({
    //   command: "npx",
    //   args: ["-y", "exa-mcp-server", "--tools=web_search_exa"],
    //   headers: {
    //     Authorization: "4e01cf2d-7b82-4ee3-ad97-21b7fd805e78",
    //   },
    //   env: {
    //     EXA_API_KEY: "4e01cf2d-7b82-4ee3-ad97-21b7fd805e78",
    //   },
    // });
    // searchMcpClient = await createMCPClient({
    //   transport2,
    // });

    const toolSetOne = await memoryMcpClient.tools();
    // const toolSetTwo = await searchMcpClient.tools();
    const tools = {
      ...toolSetOne,
      // ...toolSetTwo,
    };

    // Build the config object
    const config = {
      model:
        provider === "groq"
          ? groq(model)
          : google(
              model
              // {
              //   useSearchGrounding: true,
              //   dynamicRetrievalConfig: {
              //     mode: "MODE_DYNAMIC",
              //     dynamicThreshold: 0.8,
              //   },
              // }
            ),
      system: systemPrompt,
      messages: messages,
      tools,
      maxSteps: 8,
      temperature: temperature,
      thinkingConfig: {
        thinkingBudget: 0,
      },
      experimental_telemetry: {
        isEnabled: true,
        functionId: "generateText",
      },
      headers: {
        "x-supermemory-api-key":
          "sm_8hvUUKvxWUP2NStDp6L6kv_GlwWxNlUFSXwDuZmmVKkCAQBFoyCVMciwIsxjvHpzjzqpCyzyiRoznkBAtXxSkkt",
        "x-sm-conversation-id": "c" + conversationId,
      },
    };

    const result = await aiGenerateText(config);
    const allToolCalls = result?.steps?.flatMap((step) => step.toolCalls);
    logger.info(`Total tool calls: ${allToolCalls.length}`);

    return result.text;
  } finally {
    // await memoryMcpClient?.close();
    // await searchMcpClient?.close();
  }
}

module.exports = {
  generateText,
};
